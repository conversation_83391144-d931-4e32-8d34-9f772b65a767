/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const {getDefaultConfig} = require('metro-config');

module.exports = (async () => {
  const {
    resolver: {sourceExts, assetExts},
  } = await getDefaultConfig();

  return {
    transformer: {
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: true,
        },
      }),
    },
    resolver: {
      assetExts: [...assetExts, 'bin', 'txt', 'jpg', 'png', 'json', 'gif', 'webp', 'svg'],
      sourceExts: [...sourceExts, 'js', 'jsx', 'ts', 'tsx'],
    },
  };
})();
